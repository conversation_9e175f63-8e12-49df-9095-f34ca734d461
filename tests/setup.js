require('dotenv').config({ path: '.env.test', quiet: true });

// Set test environment
process.env.NODE_ENV = 'test';

const { sequelize } = require('../src/models');
const QdrantServiceMock = require('./mocks/QdrantServiceMock');
const GoogleAiServiceMock = require('./mocks/GoogleAiServiceMock');

// Mock Qdrant client globally to avoid connection failures
jest.mock('@qdrant/js-client-rest', () => {
  return {
    QdrantClient: jest.fn().mockImplementation(() => new QdrantServiceMock()),
  };
});

// Mock Google AI service globally to avoid expensive API calls
jest.mock('@google/genai', () => {
  return {
    GoogleGenAI: jest.fn().mockImplementation(() => new GoogleAiServiceMock()),
  };
});

// Mock Redis and BullMQ globally to prevent any Redis connections in tests
jest.mock('../src/config/redis', () => ({
  getRedisConnection: jest.fn(() => ({
    quit: jest.fn().mockResolvedValue(undefined),
    status: 'ready',
  })),
  closeRedisConnection: jest.fn().mockResolvedValue(undefined),
  isRedisConnected: jest.fn(() => false), // Always return false in tests
  _redisConnection: {
    getInstance: jest.fn(() => ({
      quit: jest.fn().mockResolvedValue(undefined),
      status: 'ready',
    })),
    close: jest.fn().mockResolvedValue(undefined),
    isConnected: jest.fn(() => false),
  },
}));

jest.mock('bullmq', () => ({
  Queue: jest.fn().mockImplementation(() => ({
    add: jest.fn().mockResolvedValue({ id: `job-${Date.now()}` }),
    close: jest.fn().mockResolvedValue(undefined),
  })),
  Worker: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    close: jest.fn().mockResolvedValue(undefined),
  })),
}));

// Mock Bull Board components to prevent issues in app.js
jest.mock('@bull-board/api', () => ({
  createBullBoard: jest.fn(() => ({})),
  BullMQAdapter: jest.fn().mockImplementation(() => ({})),
}));

jest.mock('@bull-board/express', () => ({
  ExpressAdapter: jest.fn().mockImplementation(() => ({
    setBasePath: jest.fn(),
    getRouter: jest.fn(() => (_req, _res, next) => next()),
  })),
}));

// Mock ioredis to prevent Redis connections
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    quit: jest.fn().mockResolvedValue(undefined),
    status: 'ready',
    on: jest.fn(),
  }));
});

// Mock all job classes globally to prevent Redis connections and queue operations
jest.mock('../src/jobs/ApplicationJob', () => {
  return class MockApplicationJob {
    constructor() {
      this.queueName = 'default';
      this.redis = null;
      this.queue = {
        add: jest.fn().mockResolvedValue({ id: `job-${Date.now()}` }),
        close: jest.fn().mockResolvedValue(undefined),
      };
    }

    static async perform_async(data = {}, options = {}) {
      return { id: `job-${Date.now()}`, data, options };
    }

    async perform(_data) {
      throw new Error(`${this.constructor.name} must implement the perform method`);
    }

    async before_perform(_data) {
      // Mock implementation
    }

    async after_perform(_data, _result) {
      // Mock implementation
    }

    async on_failure(_data, _error) {
      // Mock implementation
    }

    getQueue() {
      return this.queue;
    }

    getRedis() {
      return this.redis;
    }

    async close() {
      // Mock implementation
    }
  };
});

// Mock specific job classes to prevent any Redis operations
jest.mock('../src/jobs/SetJobDescriptionJob', () => {
  const MockApplicationJob = require('../src/jobs/ApplicationJob');

  return class SetJobDescriptionJob extends MockApplicationJob {
    static async perform_async(data = {}, options = {}) {
      return { id: `set-job-desc-${Date.now()}`, data, options };
    }

    async perform(data) {
      return { vacancy_id: data.vacancy_id, status: 'completed' };
    }
  };
});

jest.mock('../src/jobs/SetVacancyGroupVariablesJob', () => {
  const MockApplicationJob = require('../src/jobs/ApplicationJob');

  return class SetVacancyGroupVariablesJob extends MockApplicationJob {
    static async perform_async(data = {}, options = {}) {
      return { id: `set-vacancy-vars-${Date.now()}`, data, options };
    }

    async perform(data) {
      return { vacancy_id: data.vacancy_id, status: 'completed' };
    }
  };
});

// Mock the jobs index file to return mocked job classes
jest.mock('../src/jobs', () => {
  const MockApplicationJob = require('../src/jobs/ApplicationJob');

  class MockSetJobDescriptionJob extends MockApplicationJob {
    static async perform_async(data = {}, options = {}) {
      return { id: `set-job-desc-${Date.now()}`, data, options };
    }
  }

  class MockSetVacancyGroupVariablesJob extends MockApplicationJob {
    static async perform_async(data = {}, options = {}) {
      return { id: `set-vacancy-vars-${Date.now()}`, data, options };
    }
  }

  return {
    SetJobDescriptionJob: MockSetJobDescriptionJob,
    SetVacancyGroupVariablesJob: MockSetVacancyGroupVariablesJob,
  };
});

// Mock VacancyGroupVariableService to avoid complex SQL operations during tests
jest.mock('../src/services/VacancyGroupVariableService', () => {
  const originalModule = jest.requireActual('../src/services/VacancyGroupVariableService');

  return class VacancyGroupVariableService extends originalModule {
    async calculateUserVacancyVariableScores(_jobVacancyId) {
      // Mock implementation that doesn't execute complex SQL
      return Promise.resolve();
    }
  };
});

// Mock OnetService to avoid database queries to non-existent onet_occupations table
jest.mock('../src/services/OnetService', () => {
  return class OnetService {
    async getOccupations(_onetsocCodes) {
      // Mock implementation that returns sample occupation data
      return [
        {
          onetsoc_code: '15-1252.00',
          title: 'Software Developers, Applications',
          description:
            'Develop, create, and modify general computer applications software or specialized utility programs.',
        },
        {
          onetsoc_code: '15-1251.00',
          title: 'Computer Programmers',
          description:
            'Create, modify, and test the code and scripts that allow computer applications to run.',
        },
      ];
    }

    async getTasks(_onetsocCodes) {
      // Mock implementation that returns sample task data
      return [
        {
          onetsoc_code: '15-1252.00',
          task: 'Analyze user requirements to derive technical software design and performance requirements.',
        },
        {
          onetsoc_code: '15-1252.00',
          task: 'Debug, maintain, and update existing software applications.',
        },
        {
          onetsoc_code: '15-1251.00',
          task: 'Write, update, and maintain computer programs or software packages.',
        },
      ];
    }
  };
});

// Mock job description generation service to avoid external API calls
jest.mock('../src/services/job_vacancy/GenerateJobDescService', () => {
  const originalModule = jest.requireActual('../src/services/job_vacancy/GenerateJobDescService');

  return class GenerateJobDescService extends originalModule {
    async generateJobDesc(_name, _topUserIds, _jobLevelName) {
      return {
        jobTitle: 'Software Engineer',
        jobDescription: {
          key_responsibilities: ['Develop and maintain software applications'],
          qualifications: ["Bachelor's degree in Computer Science"],
          competencies: ['JavaScript', 'Node.js'],
          success_metrics: ['Deliver projects on time'],
        },
        onetsocCodes: ['15-1132.00'],
      };
    }
  };
});

// Mock job vacancy service to avoid external API calls
jest.mock('../src/services/JobVacancyService', () => {
  const originalModule = jest.requireActual('../src/services/JobVacancyService');

  return class JobVacancyService extends originalModule {
    async setJobDesc(_data) {
      return;
    }

    async setVacancyGroupVariables(_data) {
      return;
    }
  };
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(3000);

// beforeEach(async () => {
//   // CLS (Continuation-Local Storage) is used to track the transaction
//   // across asynchronous operations. We start a new "context" for each test.
//   await namespace.runPromise(async () => {
//     const transaction = await sequelize.transaction();
//     namespace.set('transaction', transaction);
//   });
// });

// Clean up after each test
afterEach(async () => {
  jest.clearAllMocks();

  // // Retrieve the transaction from the CLS
  // const transaction = namespace.get('transaction');
  // if (transaction) {
  //   // Rollback the transaction to undo all changes made during the test
  //   await transaction.rollback();
  // }
});

beforeAll(async () => {
  // Ensure database connection is available
  try {
    await sequelize.authenticate();
  } catch (error) {
    console.error('Failed to connect to test database:', error);
    throw error;
  }
});

afterAll(async () => {
  // Close database connection after all tests
  try {
    await sequelize.close();
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
});
