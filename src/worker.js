const { Worker } = require('bullmq');
const { getRedisConnection, closeRedisConnection } = require('./config/redis');
const config = require('./config/config');

// Import all job classes from the jobs index
const jobClasses = require('./jobs');

class JobWorker {
  constructor() {
    this.redis = getRedisConnection();
    this.worker = null;
    this.isShuttingDown = false;

    // Use job classes from the jobs index
    this.jobClasses = jobClasses;
  }

  /**
   * Start the worker
   */
  async start() {
    console.log('Starting BullMQ worker...');

    this.worker = new Worker(
      'default',
      async job => {
        const { name, data } = job;

        console.log(`Processing job: ${name} with data:`, data);

        // Find the job class by name
        const JobClass = this.jobClasses[name];
        if (!JobClass) {
          throw new Error(`Unknown job type: ${name}`);
        }

        const instance = new JobClass();

        try {
          await instance.before_perform(data);
          const result = await instance.perform(data);
          await instance.after_perform(data, result);
          return result;
        } catch (error) {
          await instance.on_failure(data, error);
          throw error;
        } finally {
          // Clean up job instance resources
          if (typeof instance.close === 'function') {
            await instance.close();
          }
        }
      },
      {
        connection: this.redis,
        concurrency: config.queue.concurrency,
        removeOnComplete: config.queue.defaultJobOptions.removeOnComplete,
        removeOnFail: config.queue.defaultJobOptions.removeOnFail,
      },
    );

    // Worker event handlers
    this.worker.on('completed', job => {
      console.log(`✅ Job ${job.name} (${job.id}) completed successfully`);
      if (job.returnvalue) {
        console.log('Result:', job.returnvalue);
      }
    });

    this.worker.on('failed', (job, err) => {
      console.error(`❌ Job ${job.name} (${job.id}) failed:`, err.message);
      console.error('Stack trace:', err.stack);
    });

    this.worker.on('error', err => {
      console.error('🚨 Worker error:', err);
    });

    this.worker.on('stalled', jobId => {
      console.warn(`⚠️  Job ${jobId} stalled`);
    });

    this.worker.on('progress', (job, progress) => {
      console.log(`📊 Job ${job.name} (${job.id}) progress: ${progress}%`);
    });

    // Setup graceful shutdown
    this.setupGracefulShutdown();

    console.log(`🚀 Worker started with concurrency: ${config.queue.concurrency}`);
    console.log('📋 Registered job types:', Object.keys(this.jobClasses));
    console.log('⏳ Waiting for jobs...');
  }

  /**
   * Setup graceful shutdown handlers
   */
  setupGracefulShutdown() {
    const shutdown = async signal => {
      if (this.isShuttingDown) {
        console.log('🔄 Shutdown already in progress...');
        return;
      }

      this.isShuttingDown = true;
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

      try {
        if (this.worker) {
          console.log('⏹️  Stopping worker...');
          await this.worker.close();
          console.log('✅ Worker stopped successfully');
        }

        // Close the centralized Redis connection
        await closeRedisConnection();
        console.log('👋 Graceful shutdown completed');
        process.exitCode = 0;
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exitCode = 1;
      }
    };

    // Handle different shutdown signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon

    // Handle uncaught exceptions
    process.on('uncaughtException', error => {
      console.error('🚨 Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }

  /**
   * Get worker statistics
   */
  async getStats() {
    if (!this.worker) {
      return null;
    }

    return {
      isRunning: !this.worker.closing,
      concurrency: config.queue.concurrency,
      registeredJobs: Object.keys(this.jobClasses),
    };
  }
}

// Start the worker if this file is run directly
if (require.main === module) {
  const worker = new JobWorker();

  worker.start().catch(error => {
    console.error('❌ Failed to start worker:', error);
    process.exitCode = 1;
  });
}

module.exports = JobWorker;
